name: acko_flutter
description: Flutter module.
version: 1.0.0+1
publish_to: none
environment:
  sdk: ">=3.1.5 <4.0.0"
  flutter: 3.29.2
dependencies:
  flutter:
    sdk: flutter

  ##### CORE DEPENDENCIES #####
  dio: ^5.4.3+1
  graphql: ^5.1.3
  dio_cookie_manager: ^3.1.1
  dio_cache_interceptor: ^3.5.0
  flutter_bloc: ^8.1.5
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.10+1
  equatable: ^2.0.5
  flutter_inappwebview: ^6.0.0
  connectivity_plus: ^6.1.2
  package_info_plus: ^8.1.4
  device_info_plus: ^11.2.2
  fluttertoast: 8.2.10
  permission_handler: ^12.0.0
  shared_preferences: ^2.2.3
  path_provider: ^2.1.3
  intl: ^0.19.0
  url_launcher: ^6.2.6
  video_player: ^2.9.2
  lottie: ^3.1.0
  flutter_segment:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: plugins/flutter-segment
      ref: utilities_plugin
  ##### CORE DEPENDENCIES #####

  ##### FIREBASE DEPENDENCIES #####
  firebase_crashlytics: ^4.3.1
  firebase_analytics: ^11.4.1
  firebase_core: ^3.10.1
  firebase_performance: ^0.10.1+1
  firebase_remote_config: ^5.3.1
  firebase_core_platform_interface: ^5.4.0
  firebase_messaging: ^15.2.1
  ##### FIREBASE DEPENDENCIES #####

  ##### OTHER DEPENDENCIES #####
  syncfusion_flutter_charts: ^20.4.54
  google_maps_flutter: ^2.6.1
  flutter_windowmanager:
    path: modules/plugins/flutter_windowmanager
  flutter_slidable: ^3.1.0
  smooth_page_indicator: ^1.1.0
  qr_code_scanner:
    path: modules/plugins/qr_code_scanner
  camera: ^0.10.5+9
  path: ^1.8.2
  sprintf: ^7.0.0
  geocoding: ^3.0.0
  amplitude_flutter: ^4.3.0
  ##### OTHER DEPENDENCIES #####

  ##### ACKO INTERNAL DEPENDENCIES #####
  analytics:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: analytics_module
      ref: utilities_plugin

  networking_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: networking_module
      ref: nw_migration
  design_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: design_module
      ref: design-migration
  sdui:
    path: modules/sdui-master
  growthbook_sdk_flutter:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: growth_book
      ref: utilities_plugin
  acko_web_view_module:
    path: modules/acko_web_view_module
  session_manager_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: session_manager_module
      ref: utilities_plugin
  utilities:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: utilities
      ref: utilities_plugin
  policy_details:
    path: modules/policy_details
  acko_core_utilities:
    path: modules/acko_core_utilities
  acko_logger:
    path: modules/acko_logger

dependency_overrides:
  web_socket_channel: 2.3.0
  accessibility_tools: 1.0.0
  video_player_android: 2.8.2

dev_dependencies:
  test: any
  flutter_driver:
    sdk: flutter
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.2

flutter:
  uses-material-design: true
  assets:
    - assets/security/
    - assets/images/
    - assets/images/travel/
    - assets/images/home/
    - assets/images/ev_brands_transparent/
    - assets/resolution_images/1.5x/
    - assets/resolution_images/2.0x/
    - assets/resolution_images/3.0x/
    - assets/resolution_images/
    # - assets/images/FNOL/Bike/
    - assets/anim/
    - assets/js/
    - assets/images/new_images/
    - assets/images/acko_payments/
    - assets/images/mobile_brands/
    - assets/images/explore_services/
    - assets/images/acko_drive/
    - assets/images/acko_awards/
    - assets/images/videos/
    - assets/video/
  fonts:
    #    - family: inter_bold
    #      fonts:
    #        - asset: fonts/inter_bold.ttf
    #    - family: inter_medium
    #      fonts:
    #        - asset: fonts/inter_medium.ttf
    #    - family: inter_regular
    #      fonts:
    #        - asset: fonts/inter_regular.ttf
    #    - family: inter_semi_bold
    #      fonts:
    #        - asset: fonts/inter_semi_bold.ttf
    - family: euclid_circularB_bold
      fonts:
        - asset: fonts/euclid_circularB_bold.ttf
    - family: euclid_circularB_regular
      fonts:
        - asset: fonts/euclid_circularB_regular.ttf
    - family: euclid_circularB_semi_bold
      fonts:
        - asset: fonts/euclid_circularB_semi_bold.ttf
    - family: euclid_circularB_medium
      fonts:
        - asset: fonts/euclid_circularB_medium.ttf
