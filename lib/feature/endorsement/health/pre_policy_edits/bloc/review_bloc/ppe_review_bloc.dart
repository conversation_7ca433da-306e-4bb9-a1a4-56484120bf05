import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/endorsement/acko_doc_upload/model/acko_doc_model.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_instrumentation_mixin.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/review_bloc/ppe_review_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

class PPEReviewCubit extends AckoSafeCubit<PPEReviewState>
    with HealthJourneyManagerInstrumentationMixin {
  final _repo = JourneyManagerRepository();
  final String proposalId;
  PolicyChangesData policyChangesData = new PolicyChangesData();
  final bool? mandateCreationFlow;

  PPEReviewCubit({required this.proposalId, this.prePolicyEditingResponse, this.mandateCreationFlow = false})
      : super(Initial());

  HealthJourneyManagerUtils _ppeUtils = HealthJourneyManagerUtils();
  HealthJourneyManagerResponse? prePolicyEditingResponse;
  PrePolicyEditNodes? nextNode;

  bool checkboxValue = true;
  String? phoneNumber;
  String? entityReferenceId;

  Future<void> getEditsData({bool force = false}) async {
    emit(Loading());
    phoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    if (force || prePolicyEditingResponse == null) {
      Map<String, dynamic> requestBody = {
        "current_node_id": PrePolicyEditNodes.HYDRATE_DETAILS.value,
        "edit": {
          "entity_id": proposalId,
          "journey": "health_pre_policy_edit",
          "entity_type": "proposal",
        },
        "input_data_id": null
      };

      prePolicyEditingResponse =
          await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
    }

    nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();
    entityReferenceId =
        prePolicyEditingResponse?.edit?.entityDetails?.editReferenceId;

    Map<String, String?> insuredNameMap = HealthJourneyManagerUtils()
        .getInsuredIdNameMap(prePolicyEditingResponse);

    final deltaGrossPremium = prePolicyEditingResponse?.edit?.deltaPremium?.deltaGrossPremium;

    policyChangesData.addChangesFromEditRequest(
        prePolicyEditingResponse?.edit?.editRequest,
        prePolicyEditingResponse?.edit?.documents,
        insuredNameMap,
        prePolicyEditingResponse?.edit?.entityDetails);

    policyChangesData.amountToBePaid(prePolicyEditingResponse?.edit?.deltaPremium?.adhocPayment);
    policyChangesData
        .mapToPremiumChange(prePolicyEditingResponse?.edit?.deltaPremium);

    if (policyChangesData.hasCheckBox) {
      checkboxValue = false;
    }

    String? draftStatus = prePolicyEditingResponse?.edit?.draftState;

    DraftState draftState = getDraftState(
        prePolicyEditingResponse?.edit?.draftState,
        prePolicyEditingResponse?.edit?.editRequest,
        prePolicyEditingResponse?.edit?.deltaPremium);

    bool allDocsUploaded = areAllDocsUploaded();
    bool enableCta = checkboxValue && allDocsUploaded;
    bool isEditable =
        prePolicyEditingResponse?.edit?.entityDetails?.isEditAllowed ?? true;

    emit(Loaded(
        policyChangesData: policyChangesData,
        totalEdits: prePolicyEditingResponse?.edit?.getTotalPendingEdits,
        documents: prePolicyEditingResponse?.edit?.documents ?? [],
        draftState: draftState,
        enableCta: enableCta,
        isEditable: isEditable,
        premiumChangesData: policyChangesData.premiumChange,
        basbaProposal:
            prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal,
        isActiveMandate:
            prePolicyEditingResponse?.edit?.entityDetails?.isActiveMandate,
        mandateDetails:
            prePolicyEditingResponse?.edit?.entityDetails?.mandateDetails, draftDays:
        prePolicyEditingResponse?.edit?.entityDetails?.mandateDetails?.draftDays));
  }

  bool areAllDocsUploaded() {
    bool allDocsUploaded = true;

    policyChangesData.memberChanges?.forEach((member) {
      if (member.documents?.documentModel != null) {
        member.documents!.documentModel!.forEach((doc) {
          if (doc.isUploaded != true) {
            allDocsUploaded = false;
          }
        });
      }
    });

    return allDocsUploaded;
  }

  deleteDraft() async {
    emit(CancelDraftLoading());

    String? editGroupId = prePolicyEditingResponse?.edit?.editGroupId ??
        prePolicyEditingResponse?.edit?.editRequest?.firstOrNull?.editGroupId;

    if (editGroupId != null) {
      Map<String, dynamic> requestBody = {
        "current_node_id": PrePolicyEditNodes.CANCEL_DRAFT.value,
        "edit": {
          "entity_id": proposalId,
          "journey": "health_pre_policy_edit",
          "entity_type": "proposal",
          "edit_group_action": {"id": editGroupId, "status": "CANCELLED"}
        },
        "input_data_id": null
      };

      HealthJourneyManagerResponse? ppeResponse =
          await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
      if (ppeResponse.error == null) {
        prePolicyEditingResponse = ppeResponse;
        emit(CancelDraftSuccess());
      } else {
        emit(CancelDraftError());
      }
    }
  }

  Map<String, dynamic> createUploadRequestBody(
      List<Documents> docs, List<EditRequest> editRequest) {
    return {
      "current_node_id": PrePolicyEditNodes.REVIEW.value,
      "edit": {
        "entity_id": proposalId,
        "journey": "health_pre_policy_edit",
        "entity_type": "proposal",
        "edit_request": editRequest.map((element) => element.toJson()).toList()
      },
      "input_data_id": null
    };
  }

  deleteDoc(AckoDocumentModel? doc, String? title) {
    if (doc == null || title == null) return;

    if (title.containsIgnoreCase("porting")) {
      policyChangesData.portingChanges?.documents = null;
    } else if (title.containsIgnoreCase("policy")) {
      policyChangesData.policyChanges?.forEach((policyChange) {
        if (policyChange.documents?.id == doc.id) {
          policyChange.documents = null;
        }
      });
    } else if (title.containsIgnoreCase("member")) {
      policyChangesData.memberChanges?.forEach((policyChange) {
        if (policyChange.documents?.id == doc.id) {
          policyChange.documents = doc;
        }
      });
    }

    bool enableContinueCta = checkboxValue && areAllDocsUploaded();
    emit(CtaStateChange(enableCta: enableContinueCta));
  }

  uploadDoc(AckoDocumentModel? doc, String? title) {
    if (title.containsIgnoreCase("porting")) {
      policyChangesData.portingChanges?.documents = doc;
    } else if (title.containsIgnoreCase("policy")) {
      policyChangesData.policyChanges?.forEach((policyChange) {
        if (policyChange.documents?.id == doc?.id) {
          policyChange.documents = doc;
        }
      });
    } else if (title.containsIgnoreCase("member")) {
      policyChangesData.memberChanges?.forEach((policyChange) {
        if (policyChange.documents?.id == doc?.id) {
          policyChange.documents = doc;
        }
      });
    }

    bool enableContinueCta = checkboxValue && areAllDocsUploaded();
    emit(CtaStateChange(enableCta: enableContinueCta));
  }

  bool isAnyDocRequired(List<EditRequest>? editRequest) {
    if (editRequest == null) return false;

    return editRequest.any(
        (edit) => edit.requirements?.docsRequired.isNotNullOrEmpty ?? false);
  }

  getDraftState(String? draftStatus, List<EditRequest>? editRequest,
      DeltaPremium? deltaPremium) {
    bool paymentNotRequired = policyChangesData.isNonFinancial;
    bool refundRequired = policyChangesData.isRefundRequired;
    bool anyDocReqired = isAnyDocRequired(editRequest);

    final hasEditsInDraftGroup =
        prePolicyEditingResponse?.edit?.editGroups?.any(
              (group) => ((group.status.equalsIgnoreCase("draft") &&
                  group.edits.isNotNullOrEmpty)),
            ) ??
            false;

    final hasEmptyEditRequest =
        prePolicyEditingResponse?.edit?.editRequest?.any(
              (editRequest) => (editRequest.endorsements.isNullOrEmpty),
            ) ??
            true;

    bool isDraft = draftStatus.equalsIgnoreCase('draft') ||
        draftStatus.equalsIgnoreCase('pending');

    bool isBASBAProposal =
        prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal ?? false;
    bool isActiveMandate =
        prePolicyEditingResponse?.edit?.entityDetails?.isActiveMandate ?? false;

    if (draftStatus.equalsIgnoreCase('draft') &&
        !paymentNotRequired &&
        !refundRequired &&
        anyDocReqired) {
      if (isBASBAProposal && !isActiveMandate) {
        return DraftState.PAYMENT_AND_DOC_REQUIRED_EXPIRED_MANDATE;
      } else if (isBASBAProposal) {
        return DraftState.PAYMENT_AND_DOC_REQUIRED_ACTIVE_MANDATE;
      } else {
        return DraftState.PAYMENT_AND_DOC_REQUIRED;
      }
    } else if (draftStatus.equalsIgnoreCase('draft') &&
        !paymentNotRequired &&
        !refundRequired) {
      if (isBASBAProposal && !isActiveMandate) {
        return DraftState.PAYMENT_REQUIRED_EXPIRED_MANDATE;
      } else if (isBASBAProposal) {
        return DraftState.PAYMENT_REQUIRED_ACTIVE_MANDATE;
      } else {
        return DraftState.PAYMENT_REQUIRED;
      }
    } else if (isDraft && draftStatus.equalsIgnoreCase('pending')) {
      return DraftState.UNDER_REVIEW;
    } else if (draftStatus.equalsIgnoreCase('draft') &&
        refundRequired &&
        anyDocReqired) {
      if (isBASBAProposal && !isActiveMandate) {
        return DraftState.REFUND_AND_DOC_REQUIRED_EXPIRED_MANDATE;
      } else if (isBASBAProposal) {
        return DraftState.REFUND_AND_DOC_REQUIRED_ACTIVE_MANDATE;
      } else {
        return DraftState.REFUND_AND_DOC_REQUIRED;
      }
    } else if (draftStatus.equalsIgnoreCase('draft') && refundRequired) {
      if (isBASBAProposal && !isActiveMandate) {
        return DraftState.REFUND_REQUIRED_EXPIRED_MANDATE;
      } else if (isBASBAProposal) {
        return DraftState.REFUND_REQUIRED_ACTIVE_MANDATE;
      } else {
        return DraftState.REFUND_REQUIRED;
      }
    } else if (draftStatus.equalsIgnoreCase('draft') && anyDocReqired) {
      return DraftState.DOCUMENT_REQUIRED;
    } else
      return DraftState.NONE;

    /// case non financial and no doc (or none)
  }

  Future<void> onReviewContinue() async {
    emit(FullPageLoading());
    Map<String, dynamic>? body = createUploadRequestBody(
        [], prePolicyEditingResponse?.edit?.editRequest ?? []);

    final response = await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT,
        body); // prePolicyEditingResponse = await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, body);
    emit(FullPageLoaded());
    await Future.delayed(Duration(milliseconds: 100));
    if (response.error == null) {
      prePolicyEditingResponse = response;
      nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();
      emit(NavigateNext());
    } else {
      emit(ErrorToast());
    }
  }

  updateCheckBoxValue(bool value) {
    checkboxValue = value;
    bool enableContinueCta = checkboxValue && areAllDocsUploaded();
    emit(CtaStateChange(enableCta: enableContinueCta));
  }

  triggerTapEvent(HLTrackEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerTapAnalyticalEventsForPPE(
      trackEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      // phone: phoneNumber,
      origin: "SUREOS",
    );
  }

  triggerPageEvent(HLPageEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerPageAnalyticalEventsForPPE(
      pageEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      // phone: phoneNumber,
      origin: "SUREOS",
    );
  }
}
