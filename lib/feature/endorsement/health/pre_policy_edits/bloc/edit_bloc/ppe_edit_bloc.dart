import 'dart:convert';
import 'dart:math';

import 'package:acko_flutter/common/model/picker_model.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_instrumentation_mixin.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_premium_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import 'ppe_edit_states.dart';

class PPEEditCubit extends AckoSafeCubit<PPEEditState>
    with HealthJourneyManagerInstrumentationMixin {
  final _repo = JourneyManagerRepository();
  EditType editType;
  final String proposalId;

  FormValues formValues = FormValues();
  HealthJourneyManagerResponse? prePolicyEditingResponse;
  PrePolicyEditNodes? nextNode;

  double scrollOffset = 0.0;
  bool scrollToBottom;
  String? phoneNumber;
  bool newSkuPlan = false;

  /// Persists the self's insured ID when user adds or removes themselves
  String? selfInsuredId;

  PPEEditCubit(
      {required this.proposalId,
      this.editType = EditType.NONE,
      this.prePolicyEditingResponse,
      this.scrollToBottom = false})
      : super(Initial());

  HealthJourneyManagerUtils _ppeUtils = HealthJourneyManagerUtils();

  Future<void> getEditsData({bool force = false}) async {
    emit(Loading());
    phoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    if (force || prePolicyEditingResponse == null) {
      Map<String, dynamic> requestBody = {
        "current_node_id": PrePolicyEditNodes.HYDRATE_DETAILS.value,
        "edit": {
          "entity_id": proposalId,
          "journey": "health_pre_policy_edit",
          "entity_type": "proposal"
        },
        "input_data_id": null
      };
      prePolicyEditingResponse =
          await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
    }

    nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();

    if (prePolicyEditingResponse != null &&
        prePolicyEditingResponse?.error == null) {
      newSkuPlan =
          true || prePolicyEditingResponse?.edit?.cohort == 'new_sku_2025';
      formValues.populateFormValuesFromPrePolicyEditsResponse(
          prePolicyEditingResponse?.edit?.oldValue,
          prePolicyEditingResponse?.edit?.packagePlans,
          prePolicyEditingResponse?.edit?.deductibles,
          paymentDate:
              prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
          isOld: true);
      formValues.populateFormValuesFromPrePolicyEditsResponse(
          prePolicyEditingResponse?.edit?.newValue,
          prePolicyEditingResponse?.edit?.packagePlans,
          prePolicyEditingResponse?.edit?.deductibles,
          paymentDate:
              prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
          isOld: false);
      formValues.updateMemberStatus();
      formValues.sortNewByOldValues();

      String? familyConstraintsData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

      List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
          ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
          : null;

      EndorsementFormValidatorConfig? config = parsedList != null
          ? EndorsementFormValidatorConfig.fromJson(parsedList)
          : null;

      formValues.setConfig(
          prePolicyEditingResponse?.edit?.familyConstrains ?? config!);

      int totalEdits = formValues.calculateTotalEdits;
      bool enableCta = totalEdits != 0 && formValues.areAllEditsValid;
      PremiumDetails premiumDetails =
          mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

      emit(Loaded(
          formValues: formValues,
          totalEdits: totalEdits,
          amount: premiumDetails.toBePaid?.toInt(),
          enableCta: enableCta,
          validationConfig:
              prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
          refreshOverview: scrollToBottom,
          showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
              formValues.getMaxMemberLimit()));
      await Future.delayed(Duration(milliseconds: 200));
      emit(AutoScroll(scrollToBottom: scrollToBottom));
      scrollToBottom = false;
    } else {
      emit(Error());
    }
  }

  PremiumDetails mapToPremiumDetails(DeltaPremium? deltaPremium) {
    return PremiumDetails(
      totalPremiumDue: deltaPremium?.newInstallment,
      premiumAlreadyPaid: deltaPremium?.paidAmount,
      toBePaid: deltaPremium?.adhocPayment,
      dueTime: deltaPremium?.premiumPendingCount,
    );
  }

  void getPolicyChangesData() {
    final deltaGrossPremium =
        prePolicyEditingResponse?.edit?.deltaPremium?.deltaGrossPremium;
    PolicyChangesData policyChangesData = new PolicyChangesData();
    policyChangesData.addChangesFromForm(formValues);
    policyChangesData.amountToBePaid(
        prePolicyEditingResponse?.edit?.deltaPremium?.adhocPayment);
    policyChangesData
        .mapToPremiumChange(prePolicyEditingResponse?.edit?.deltaPremium);
    emit(ReviewSheetLoaded(
        policyChangesData: policyChangesData,
        totalEdits: policyChangesData.totalEdits));
  }

  void updatePortingDetails({required String key, dynamic newValue}) {
    editType = EditType.PORTING;
    switch (key) {
      case 'current_policy_expiry_date':
        formValues.newValues?.portingDetails?.portingDate = newValue;
        break;
    }
    updateForNonFinancialEdits();
  }

  /// when sync not required
  updateForNonFinancialEdits({bool hasError = false}) async {
    String? familyConstraintsData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

    List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
        ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
        : null;

    EndorsementFormValidatorConfig? config = parsedList != null
        ? EndorsementFormValidatorConfig.fromJson(parsedList)
        : null;

    formValues
        .setConfig(prePolicyEditingResponse?.edit?.familyConstrains ?? config!);

    int totalEdits = formValues.calculateTotalEdits;
    // FIXED: If there are edits in draft state, enable continue button
    // Don't block user from proceeding if they have made changes
    bool enableCta = totalEdits != 0 && !hasError;
    PremiumDetails premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

    emit(Loaded(
        formValues: formValues,
        addMemberFlow: false,
        redirectToNextNode: false,
        amount: premiumDetails.toBePaid?.toInt(),
        enableCta: enableCta,
        totalEdits: totalEdits,
        validationConfig:
            prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
        refreshOverview: true,
        showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
            formValues.getMaxMemberLimit()));
    await Future.delayed(Duration(milliseconds: 200));
    emit(AutoScroll());
  }

  void updatePolicyDetails({required String key, dynamic newValue}) {
    editType = EditType.POLICY;

    bool changesMade = true;

    switch (key) {
      case 'plan_name':
        formValues.newValues?.policyDetails?.packageName = newValue;

        /// plan name change not allowed from app
        break;
      case 'sum_insured':
        {
          PickerModel siValues;
          siValues = PickerModel(
              id: newValue ?? '',
              name: HealthJourneyManagerUtils()
                  .formatCurrencyWithUnits(newValue));
          if (formValues.newValues?.policyDetails?.sumInsured?.id ==
              siValues.id) changesMade = false;
          formValues.newValues?.policyDetails?.sumInsured = siValues;
        }
        break;
      case 'deductible':
        {
          PickerModel deductibleValues;
          deductibleValues = PickerModel(
              id: newValue ?? '',
              name: HealthJourneyManagerUtils()
                  .formatCurrencyWithUnits(newValue));
          if (formValues.newValues?.policyDetails?.deductible?.id ==
              deductibleValues.id) changesMade = false;
          formValues.newValues?.policyDetails?.deductible = deductibleValues;
        }
        break;
    }

    if (changesMade) syncApiAndFormValues(nextNode: PrePolicyEditNodes.PREMIUM);

    /// is financial check not required both are financial edits
  }

  void findAndUpdateMemberDetails(
      {required String key,
      dynamic newValue,
      required String? insuredId,
      required bool isProposer,
      bool isFinancial = false,
      bool hasError = false}) {
    editType = EditType.MEMBER;

    if (hasError) {
      updateForNonFinancialEdits(hasError: true);
      return;
    }

    MemberDetails? member;
    if (isProposer) {
      member = formValues.newValues?.proposerDetails;
      if (member != null) {
        // Store original self-exclusion state before update
        bool originalIsSelfExcluded = member.isSelfExcluded;
        bool originalWasSelfExcluded = member.wasSelfExcluded;

        MemberDetails updatedProposerDetails =
            updateMemberDetails(member: member, key: key, newValue: newValue);

        // CRITICAL: Ensure self-exclusion state is preserved during edits
        if (originalIsSelfExcluded || originalWasSelfExcluded) {
          updatedProposerDetails.isSelfExcluded = originalIsSelfExcluded;
          updatedProposerDetails.wasSelfExcluded = originalWasSelfExcluded;
        }

        formValues.newValues?.proposerDetails = updatedProposerDetails;
      }
    } else {
      member = (formValues.newValues?.memberDetails ?? []).firstWhere(
        (member) => member?.insuredId.equalsIgnoreCase(insuredId) ?? false,
        orElse: () => null,
      );

      if (member != null) {
        final members = formValues.newValues?.memberDetails ?? [];
        final index = members.indexWhere(
            (m) => m?.insuredId?.equalsIgnoreCase(insuredId) ?? false);
        if (index != -1) {
          members.replaceRange(index, index + 1, [
            updateMemberDetails(member: member, key: key, newValue: newValue)
          ]);
        }

        formValues.newValues?.memberDetails = members;
      }
    }

    if (isFinancial) {
      syncApiAndFormValues(nextNode: PrePolicyEditNodes.PREMIUM);
    } else
      updateForNonFinancialEdits();
  }

  MemberDetails updateMemberDetails(
      {required MemberDetails member,
      required String key,
      required dynamic newValue}) {
    /// only editable fields can be changed
    switch (key) {
      case "name":
        member = member.copyWith(name: newValue);
        break;
      case "gender":
        member = member.copyWith(gender: newValue);
        break;
      case "dob":
        member = member.copyWith(dateOfBirth: newValue);
        break;
      case "height":
        member = member.copyWith(height: newValue);
        break;
      case "weight":
        member = member.copyWith(weight: newValue);
        break;
      case "pincode":
        member = member.copyWith(pinCode: newValue);
        break;
      case "relation":
        member = member.copyWith(relation: newValue);
        break;
    }
    return member;
  }

  submitEditingForm() {
    syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.EDIT,
        fullPageLoader: true,
        redirectToNextNode: true);
  }

  /// sync whenever you'll be making api calls
  syncApiAndFormValues(
      {required PrePolicyEditNodes nextNode,
      bool addMemberFlow = false,
      bool redirectToNextNode = false,
      bool fullPageLoader = false,
      bool showErrorScreen = false}) async {
    if (addMemberFlow) editType = EditType.MEMBER;

    emit(ShowFormEditingLoader(fullPageLoader: fullPageLoader));
    prePolicyEditingResponse?.updateFromFormValues(formValues);
    Map<String, dynamic> requestBody =
        prePolicyEditingResponse?.toRequestBody(nextNode.value) ?? {};

    final response =
        await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);

    if (response.error != null) {
      emit(DismissFormEditingLoader(fullPageLoader: fullPageLoader));
      await Future.delayed(Duration(milliseconds: 100));
      if (showErrorScreen)
        emit(Error());
      else
        emit(ErrorToast());
      return;
    }

    prePolicyEditingResponse = response;
    this.nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode();

    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.oldValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: true);
    formValues.populateFormValuesFromPrePolicyEditsResponse(
        prePolicyEditingResponse?.edit?.newValue,
        prePolicyEditingResponse?.edit?.packagePlans,
        prePolicyEditingResponse?.edit?.deductibles,
        paymentDate: prePolicyEditingResponse?.edit?.entityDetails?.paymentDate,
        isOld: false);

    formValues.updateMemberStatus();
    formValues.sortNewByOldValues();

    String? familyConstraintsData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);

    List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
        ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
        : null;

    EndorsementFormValidatorConfig? config = parsedList != null
        ? EndorsementFormValidatorConfig.fromJson(parsedList)
        : null;

    formValues
        .setConfig(prePolicyEditingResponse?.edit?.familyConstrains ?? config!);
    emit(DismissFormEditingLoader(fullPageLoader: fullPageLoader));
    await Future.delayed(Duration(milliseconds: 100));
    int totalEdits = formValues.calculateTotalEdits;
    // FIXED: If there are edits in draft state, enable continue button
    // Don't block user from proceeding if they have made changes
    bool enableCta = totalEdits != 0;
    PremiumDetails premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

    emit(Loaded(
        formValues: formValues,
        addMemberFlow: addMemberFlow,
        redirectToNextNode: redirectToNextNode,
        amount: premiumDetails.toBePaid?.toInt(),
        enableCta: enableCta,
        totalEdits: totalEdits,
        validationConfig:
            prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
        refreshOverview: true,
        showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
            formValues.getMaxMemberLimit()));
    await Future.delayed(Duration(milliseconds: 200));
    emit(AutoScroll());
  }

  /// Validates if removing a member would leave only children without adults
  bool _wouldLeaveOnlyChildren(String insuredIdToRemove) {
    // Get all current members (excluding the one being removed)
    List<MemberDetails?> remainingMembers = [];

    // Add proposer if not being removed and not self-excluded
    if (formValues.newValues?.proposerDetails?.insuredId != insuredIdToRemove &&
        formValues.newValues?.proposerDetails?.isSelfExcluded != true) {
      remainingMembers.add(formValues.newValues?.proposerDetails);
    }

    // Add other members (excluding the one being removed and already removed ones)
    formValues.newValues?.memberDetails?.forEach((member) {
      if (member?.insuredId != insuredIdToRemove && member?.isRemoved != true) {
        remainingMembers.add(member);
      }
    });

    // Check if any remaining member is an adult
    bool hasAdult = false;
    for (var member in remainingMembers) {
      if (member?.relation != null) {
        String relation = member!.relation!.toLowerCase();
        // Adult relationships: self, spouse, parent, parent-in-law
        if (relation.contains('self') ||
            relation.contains('spouse') ||
            relation.contains('parent')) {
          hasAdult = true;
          break;
        }
      }
    }

    // If no adults remain, check if there are any children
    bool hasChildren = false;
    for (var member in remainingMembers) {
      if (member?.relation != null) {
        String relation = member!.relation!.toLowerCase();
        if (relation.contains('child')) {
          hasChildren = true;
          break;
        }
      }
    }

    // Return true if there would be children but no adults
    return hasChildren && !hasAdult;
  }

  /// Checks if the member being removed is the proposer
  bool _isProposerRemoval(String insuredId) {
    // First check if this insuredId belongs to proposer
    if (formValues.newValues?.proposerDetails?.insuredId == insuredId) {
      // Additional check: ensure this is actually the proposer and not a regular member
      // with the same insuredId (which shouldn't happen but let's be safe)
      bool isActualProposer =
          formValues.newValues?.proposerDetails?.isProposer == true;

      // Also check if this insuredId exists in memberDetails - if it does, it's a regular member
      bool existsInMemberDetails = formValues.newValues?.memberDetails?.any(
              (member) =>
                  member?.insuredId == insuredId &&
                  member?.isProposer != true) ??
          false;

      // Only consider it proposer removal if it's marked as proposer AND not in memberDetails
      return isActualProposer && !existsInMemberDetails;
    }
    return false;
  }

  /// Validates member removal and shows appropriate bottom sheet if needed
  /// Returns true if removal should proceed, false if blocked by validation
  bool validateMemberRemoval(String insuredId) {
    // Check if removing this member would leave only children
    if (_wouldLeaveOnlyChildren(insuredId)) {
      // Emit state to show adult removal restricted sheet
      emit(ShowAdultRemovalRestrictedSheet());
      return false;
    }

    // Check if this is proposer removal
    if (_isProposerRemoval(insuredId)) {
      // Emit state to show self removal confirmation sheet
      emit(ShowSelfRemovalSheet(insuredId: insuredId));
      return false;
    }

    // No validation issues, proceed with removal
    return true;
  }

  /// Proceeds with member removal after validation
  Future<void> proceedWithMemberRemoval(String insuredId) async {
    editType = EditType.MEMBER;

    emit(Loading());
    await Future.delayed(Duration(milliseconds: 100));

    // CRITICAL FIX: More robust check for proposer removal
    // Check both insuredId match AND if the member is actually marked as proposer
    bool isProposerRemoval = _isProposerRemoval(insuredId);

    if (isProposerRemoval) {
      // Handle proposer self-exclusion
      // CRITICAL FIX: Store the self's insured ID from old values (original ID) for future restoration
      // This ensures we always have the correct original insured ID regardless of current state
      if (selfInsuredId == null || selfInsuredId!.isEmpty) {
        selfInsuredId = formValues.oldValues?.proposerDetails?.insuredId ??
            formValues.newValues?.proposerDetails?.insuredId;
        print("DEBUG: Storing selfInsuredId on removal: $selfInsuredId");
        print(
            "DEBUG: oldValues insuredId: ${formValues.oldValues?.proposerDetails?.insuredId}");
        print(
            "DEBUG: newValues insuredId: ${formValues.newValues?.proposerDetails?.insuredId}");
      } else {
        print("DEBUG: selfInsuredId already exists: $selfInsuredId");
      }

      formValues.newValues?.proposerDetails?.isSelfExcluded = true;
      formValues.newValues?.proposerDetails?.wasSelfExcluded =
          false; // NEW self-exclusion
      formValues.newValues?.proposerDetails?.isRemoved = true;
    } else {
      // Handle regular member removal
      formValues.newValues?.memberDetails?.forEach((member) {
        if (member?.insuredId == insuredId) {
          member?.isRemoved = true;
        }
      });

      // CRITICAL: Ensure proposer's self-exclusion flags are NOT affected when removing regular members
      if (formValues.newValues?.proposerDetails != null) {
        // Preserve proposer's current state - don't change isSelfExcluded for regular member removals
        // Only change these flags when explicitly removing the proposer themselves
      }
    }
    await syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.PREMIUM, showErrorScreen: true);
  }

  /// Legacy method for backward compatibility - now uses validation
  Future<void> removeMember(String insuredId) async {
    // Use validation first
    if (validateMemberRemoval(insuredId)) {
      // If validation passes, proceed with removal
      await proceedWithMemberRemoval(insuredId);
    }
    // If validation fails, appropriate bottom sheet will be shown via state emission
  }

  Future<void> addMemberBack(String insuredId) async {
    editType = EditType.MEMBER;

    emit(Loading());
    await Future.delayed(Duration(milliseconds: 100));

    // Check if the member being added back is the proposer (self-addition)
    bool isProposerAddition =
        formValues.newValues?.proposerDetails?.insuredId == insuredId;

    if (isProposerAddition) {
      // Handle proposer self-addition back
      formValues.newValues?.proposerDetails?.isSelfExcluded = false;
      formValues.newValues?.proposerDetails?.isRemoved = false;

      /// Find the corresponding insuredNumber from old values
      var oldProposer = formValues.oldValues?.proposerDetails;
      if (oldProposer != null) {
        formValues.newValues?.proposerDetails?.insuredNumber =
            oldProposer.insuredNumber;
      }
    } else {
      // Handle regular member addition back
      formValues.newValues?.memberDetails?.forEach((member) {
        if (member?.insuredId == insuredId) {
          member?.isRemoved = false;

          /// Find the corresponding insuredNumber from old values
          var oldMember = formValues.oldValues?.memberDetails?.firstWhere(
            (oldMember) => oldMember?.insuredId == insuredId,
            orElse: () => null,
          );

          if (oldMember != null) {
            member?.insuredNumber = oldMember.insuredNumber;
          }
        }
      });
    }

    await syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.PREMIUM, showErrorScreen: true);
  }

  List<PickerModel> getAvailableRelationshipList(
      EndorsementFormValidatorConfig config) {
    final currentMembers = formValues.newValues?.memberDetails ?? [];
    currentMembers.add(formValues.newValues?.proposerDetails);
    final relationshipCounts = <String, int>{};

    for (var member in currentMembers) {
      if (member != null && member.relation != null) {
        relationshipCounts[member.relation!] =
            (relationshipCounts[member.relation!] ?? 0) + 1;
      }
    }

    List<PickerModel> availableRelationships = [];

    for (PickerModel pickerModel in _ppeUtils.getRelationList()) {
      bool isAvailable = true;
      for (FamilyConstrains constraint in config.familyConstrains) {
        if (constraint.option?.equalsIgnoreCase(pickerModel.id) ?? false) {
          final currentCount =
              relationshipCounts[pickerModel.id.toLowerCase()] ?? 0;
          if (currentCount >= (constraint.max ?? double.infinity)) {
            isAvailable = false;
          }
          break;
        }
      }

      if (isAvailable) {
        availableRelationships.add(pickerModel);
      }
    }

    return availableRelationships;
  }

  Future<void> addProposerSelfToPolicy({
    required String height,
    required String weight,
  }) async {
    editType = EditType.MEMBER;

    emit(Loading());
    // await Future.delayed(Duration(milliseconds: 100));

    // FIXED: Use proposer data from old values if available, otherwise use new values
    MemberDetails? proposerDetails = formValues.oldValues?.proposerDetails ??
        formValues.newValues?.proposerDetails;

    if (proposerDetails == null) {
      emit(ErrorToast());
      return;
    }

    // FIXED: Use existing insured ID if available, otherwise generate new one
    String insuredId;

    // Priority 1: Use stored selfInsuredId from previous removal
    if (selfInsuredId != null && selfInsuredId!.isNotEmpty) {
      insuredId = selfInsuredId!;
      print("DEBUG: Using stored selfInsuredId: $insuredId");
    }
    // Priority 2: Use insured ID from old values if available
    else if (formValues.oldValues?.proposerDetails?.insuredId != null &&
        formValues.oldValues!.proposerDetails!.insuredId!.isNotEmpty) {
      insuredId = formValues.oldValues!.proposerDetails!.insuredId!;
      // Store this ID for future use
      selfInsuredId = insuredId;
      print(
          "DEBUG: Using oldValues insuredId and storing as selfInsuredId: $insuredId");
    }
    // Priority 3: Generate new insured ID only if not present anywhere
    else {
      insuredId = _generateRandomId();
      selfInsuredId = insuredId;
      print(
          "DEBUG: Generated new insuredId and storing as selfInsuredId: $insuredId");
    }

    // Update proposer details with height and weight
    formValues.newValues?.proposerDetails?.height = height;
    formValues.newValues?.proposerDetails?.weight = weight;
    formValues.newValues?.proposerDetails?.isSelfExcluded = false;
    formValues.newValues?.proposerDetails?.wasSelfExcluded = false;
    formValues.newValues?.proposerDetails?.isRemoved = false;

    // Get the insured container
    final insuredContainer =
        prePolicyEditingResponse?.edit?.newValue?.insuredContainer;
    if (insuredContainer == null) {
      emit(ErrorToast());
      return;
    }

    // FIXED: Use all existing details from proposer as-is, only update height/weight from form input

    final parameters = Parameters(parameterMap: {
      'id': ParameterValue(id: null, value: insuredId, parameterVersion: null),
      'name': ParameterValue(
          id: null, value: proposerDetails.name ?? '', parameterVersion: null),
      'relation': ParameterValue(
          id: null,
          value: proposerDetails.relation ?? 'self',
          parameterVersion: null),
      'gender': ParameterValue(
          id: null,
          value: (proposerDetails.gender ?? '').toLowerCase(),
          parameterVersion: null),
      'dob': ParameterValue(
          id: null,
          value: proposerDetails.dateOfBirth ?? '',
          parameterVersion: null),
      'height': ParameterValue(
          id: null,
          value: int.parse(_ppeUtils.fromInchFeetToAckoHeight(height)),
          parameterVersion: null),
      'weight': ParameterValue(
          id: null, value: int.parse(weight), parameterVersion: null),
      // Add user_id for backend identification - use existing user_id
      'user_id': ParameterValue(
          id: null,
          value: proposerDetails.userId ?? '',
          parameterVersion: null),
      // Add pincode from existing proposer details
      'pincode': ParameterValue(
          id: null,
          value: proposerDetails.pinCode ?? '',
          parameterVersion: null),
    });

    // Create the insured object using only the required parameters with next sequential insured_number
    insuredContainer.insuredMap[insuredId] = Insured(
      insuredId: insuredId,
      parameters: parameters,
    );

    // CRITICAL: Add proposer to memberDetails list so updateInsuredDetails processes it
    // Create a new MemberDetails object using all existing proposer details, only updating height/weight and IDs
    MemberDetails proposerAsMember = MemberDetails(
      userId: proposerDetails.userId,
      insuredId:
          insuredId, // Use the determined insured ID (existing or generated)
      name: proposerDetails.name,
      gender: proposerDetails.gender,
      dateOfBirth: proposerDetails.dateOfBirth,
      height: height, // Only height is updated from form input
      weight: weight, // Only weight is updated from form input
      pinCode: proposerDetails.pinCode,
      mobileNumber: proposerDetails.mobileNumber,
      email: proposerDetails.email,
      relation: proposerDetails.relation ?? 'self',
      // Preserve all other existing details as-is
      uhid: proposerDetails.uhid,
      relationship: proposerDetails.relationship,
      age: proposerDetails.age,
      bmi: proposerDetails.bmi,
      insuredNumber: proposerDetails.insuredNumber,
      maritalStatus: proposerDetails.maritalStatus,
      role: proposerDetails.role,
      // Set appropriate flags for restoration
      isProposer: true,
      isRemoved: false,
      isNewlyAdded: true,
      isSelfExcluded: false,
      wasSelfExcluded:
          false, // Reset to false so proposer appears in member details
    );

    // Add to memberDetails list
    if (formValues.newValues?.memberDetails == null) {
      formValues.newValues?.memberDetails = [];
    }
    formValues.newValues?.memberDetails?.add(proposerAsMember);

    // Use existing API call and refresh logic
    await syncApiAndFormValues(
        nextNode: PrePolicyEditNodes.ADD_MEMBER, showErrorScreen: true);

    // CRITICAL: After API call and form rebuild, ensure proposer state is updated correctly
    // The proposer should no longer be self-excluded and should appear in member details
    if (formValues.newValues?.proposerDetails != null) {
      formValues.newValues!.proposerDetails!.isSelfExcluded = false;
      formValues.newValues!.proposerDetails!.wasSelfExcluded =
          false; // FIXED: Reset to false so proposer appears in member details
      formValues.newValues!.proposerDetails!.isRemoved = false;
      formValues.newValues!.proposerDetails!.height = height;
      formValues.newValues!.proposerDetails!.weight = weight;
      formValues.newValues!.proposerDetails!.insuredId = insuredId;
    }

    // CRITICAL: Trigger UI refresh to update sections visibility
    // This ensures proposer details section is hidden and member details section shows the proposer

    // Get validation config
    String? familyConstraintsData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.FAMILY_CONSTRAINS);
    List<Map<String, dynamic>>? parsedList = familyConstraintsData != null
        ? List<Map<String, dynamic>>.from(jsonDecode(familyConstraintsData))
        : null;
    EndorsementFormValidatorConfig? config = parsedList != null
        ? EndorsementFormValidatorConfig.fromJson(parsedList)
        : null;

    int totalEdits = formValues.calculateTotalEdits;
    bool enableCta = totalEdits != 0;
    PremiumDetails premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

    emit(Loaded(
        formValues: formValues,
        addMemberFlow: false,
        redirectToNextNode: false,
        amount: premiumDetails.toBePaid?.toInt(),
        enableCta: enableCta,
        totalEdits: totalEdits,
        validationConfig:
            prePolicyEditingResponse?.edit?.familyConstrains ?? config!,
        refreshOverview: true,
        showAddMore: (formValues.newValues?.memberDetails?.length ?? 0) + 1 <
            formValues.getMaxMemberLimit()));

    // Emit success state to navigate back
    emit(SelfAdditionSuccess());
  }

  /// Helper function to generate a random alphanumeric ID (same as InsuredContainer)
  String _generateRandomId() {
    String nowBase36 = DateTime.now().millisecondsSinceEpoch.toRadixString(36);
    String randomBase36 = Random().nextDouble().toString().substring(2);
    return nowBase36 + randomBase36;
  }

  triggerTapEvent(HLTrackEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerTapAnalyticalEventsForPPE(
      trackEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      // phone: phoneNumber,
      origin: "SUREOS",
    );
  }
}
