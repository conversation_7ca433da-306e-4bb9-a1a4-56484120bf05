name: sdui
description: SDUI make it easy to implement Server Driven UI pattern on flutter.
publish_to: none
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
#publish_to: 'none' # Remove this line if you wish to publish to pub.dev
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 0.1.198

environment:
  sdk: '>=3.1.5 <4.0.0'
  flutter: 3.29.2

# Dependencies specify othe9r packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
    analytics:
        git:
            url: **************:ackotech/AckoFlutterPlugins.git
            path: analytics_module
            ref: utilities_plugin
    cached_network_image: ^3.2.3

    carousel_slider: ^4.2.1
    cupertino_icons: ^1.0.5
    design_module:
        git:
            url: **************:ackotech/AckoFlutterPlugins.git
            path: design_module
            ref: design-migration
    firebase_remote_config: ^5.3.1
    flutter:
        sdk: flutter
    flutter_bloc: ^8.1.5
    flutter_svg: ^2.0.10+1
    lottie: ^3.1.0
    networking_module:
        git:
            url: **************:ackotech/AckoFlutterPlugins.git
            path: networking_module
            ref: nw_migration
    path: ^1.8.2
    path_provider: ^2.1.3
    shimmer: ^3.0.0
    permission_handler: ^12.0.0
    url_launcher: ^6.2.6
    utilities:
        git:
            url: **************:ackotech/AckoFlutterPlugins.git
            path: utilities
            ref: utilities_plugin

    sprintf: ^7.0.0
    device_info_plus: ^11.2.2
    fluttertoast: 8.2.10
    geocoding: ^3.0.0
    firebase_crashlytics: ^4.3.1
    session_manager_module:
        git:
            url: **************:ackotech/AckoFlutterPlugins.git
            path: session_manager_module
            ref: utilities_plugin
    path_drawing: ^1.0.1
    acko_core_utilities:
        path: ../acko_core_utilities

dev_dependencies:
    flutter_lints: ^2.0.1
    flutter_test:
        sdk: flutter
    build_runner: ^2.1.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
    # The following line ensures that the Material Icons font is
    # included with your application, so that you can use the icons in
    # the material Icons class.
    uses-material-design: true
    # To add assets to your application, add an assets section, like this:
    # An image asset can refer to one or more resolution-specific "variants", see
    # https://flutter.dev/assets-and-images/#resolution-aware.
    # For details regarding adding assets from package dependencies, see
    # https://flutter.dev/assets-and-images/#from-packages
    # To add custom fonts to your application, add a fonts section here,
    # in this "flutter" section. Each entry in this list should have a
    # "family" key with the font family name, and a "fonts" key with a
    # list giving the asset and other descriptors for the font. For
    # example:
    # fonts:
    #   - family: Schyler
    #     fonts:
    #       - asset: fonts/Schyler-Regular.ttf
    #       - asset: fonts/Schyler-Italic.ttf
    #         style: italic
    #   - family: Trajan Pro
    #     fonts:
    #       - asset: fonts/TrajanPro.ttf
    #       - asset: fonts/TrajanPro_Bold.ttf
    #         weight: 700
    #
    # For details regarding fonts from package dependencies,
    # see https://flutter.dev/custom-fonts/#from-packages

repository: https://github.com/wutsi/sdui
