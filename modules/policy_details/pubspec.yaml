name: policy_details
description: "Policy details as per asset view"
version: 0.0.1
publish_to: none
homepage:

environment:
  sdk: ">=3.1.5 <4.0.0"
  flutter: 3.29.2

dependencies:
  acko_flutter:
    path: ../../
  acko_core_utilities:
        path: ../acko_core_utilities
  analytics:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: analytics_module
      ref: utilities_plugin
  design_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: design_module
      ref: design-migration
  equatable: ^2.0.5
  firebase_remote_config: ^5.3.1
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.5
  flutter_svg: ^2.0.10+1
  intl: ^0.19.0
  networking_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: networking_module
      ref: nw_migration
  sdui:
    path: ../sdui-master
  session_manager_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: session_manager_module
      ref: utilities_plugin
  utilities:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: utilities
      ref: utilities_plugin
  


dev_dependencies:
  bloc_test: ^9.1.7
  build_runner: ^2.4.9
  flutter_gen_runner: ^5.4.0
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.3
  very_good_analysis: ">=3.1.0 <6.0.0"

flutter_gen:
  assets:
    outputs:
      package_parameter_enabled: true
  output: lib/src/core/assets/
  line_length: 80

  integrations:
    flutter_svg: true

flutter:
  assets:
    - assets/icons/
