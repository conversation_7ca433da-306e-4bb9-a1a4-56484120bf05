name: acko_web_view_module
description: Acko Web View
version: 0.0.1
homepage:
publish_to: none

environment:
  sdk: ">=3.1.5 <4.0.0"
  flutter: 3.29.2

dependencies:
  flutter:
    sdk: flutter
  flutter_inappwebview: ^6.0.0
  flutter_bloc: ^8.1.6
  lottie: ^3.1.0
  flutter_svg: ^2.0.10+1
  utilities:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: utilities
      ref: utilities_plugin
  session_manager_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: session_manager_module
      ref: utilities_plugin
  analytics:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: analytics_module
      ref: utilities_plugin
  design_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: design_module
      ref: design-migration
  acko_logger:
    path: ../acko_logger

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
