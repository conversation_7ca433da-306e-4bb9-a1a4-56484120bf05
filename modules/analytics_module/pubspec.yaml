name: analytics
description: A new Flutter package project.
version: 0.0.1
homepage:
publish_to: none

environment:
  sdk: '>=3.1.5 <4.0.0'
  flutter: 3.29.2

dependencies:
  flutter:
    sdk: flutter
  flutter_segment:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: plugins/flutter-segment
      ref: utilities_plugin

  firebase_crashlytics: ^4.3.1
  firebase_analytics: ^11.4.1
  package_info_plus: ^8.1.4
  device_info_plus: ^11.2.2
  amplitude_flutter: ^4.3.0
  intl: ^0.19.0
  utilities:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: utilities
      ref: utilities_plugin
  networking_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: networking_module
      ref: nw_migration
  session_manager_module:
    git:
      url: **************:ackotech/AckoFlutterPlugins.git
      path: session_manager_module
      ref: utilities_plugin


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

#  utility:
#    path: ../utility


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
